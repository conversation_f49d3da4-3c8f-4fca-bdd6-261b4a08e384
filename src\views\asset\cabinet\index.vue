<template>
  <div class="p-4">
    <div class="bg-white p-4 rounded-lg shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium">机柜管理</h2>
        <div class="flex gap-2">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增机柜
          </a-button>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <SyncOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-4">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="cabinet-search-form">
          <a-form-item label="机柜名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入机柜名称" allow-clear />
          </a-form-item>
          <a-form-item label="房间">
            <a-select v-model:value="searchForm.roomId" placeholder="请选择房间" class="w-50" show-search allow-clear :filter-option="filterOption">
              <a-select-option v-for="room in roomOptions" :key="room.value" :value="room.value">
                {{ room.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button @click="handleReset" class="ml-2">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'totalU'">
            <div class="flex items-center justify-center">
              <a-tag color="blue" class="px-2 py-1 text-sm font-medium rounded-6px border-none min-w-45px text-center">
                {{ record.totalU || 0 }}U
              </a-tag>
            </div>
          </template>
          <template v-if="column.key === 'usage'">
            <div class="flex flex-col gap-1">
              <a-progress :percent="calculateUsagePercent(record)" :status="getUsageStatus(record)" size="small" />
              <div class="flex justify-between text-xs text-gray-500">
                <span>已用: {{ record.useU || 0 }}U</span>
                <span>总计: {{ record.totalU || 0 }}U</span>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'useU'">
            <div class="flex items-center justify-center">
              <a-tag :color="getUsedUColor(record)" class="px-2 py-1 text-sm font-medium rounded-6px border-none min-w-45px text-center">
                {{ record.useU || 0 }}U
              </a-tag>
            </div>
          </template>
          <template v-if="column.key === 'unuseU'">
            <div class="flex items-center justify-center">
              <a-tooltip :title="formatUnusedUTooltip(record.unuseU)" placement="top">
                <a-tag color="green" class="px-2 py-1 text-sm font-medium rounded-6px border-none min-w-45px text-center">
                  {{ getUnusedUCount(record) }}U
                </a-tag>
              </a-tooltip>
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showCabinetDetail(record)">查看详情</a-button>
              <a-button type="link" size="small" @click="handleBindDevice(record)">绑定设备</a-button>
              <a-button
                type="link"
                size="small"
                @click="handleEdit(record)"
                class="transition-all duration-200 hover:transform hover:translate-y--1px"
                >编辑</a-button
              >
              <a-popconfirm title="确定要删除这个机柜吗？" @confirm="handleDelete([record.id])">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 批量操作 -->
      <div v-if="selectedRowKeys.length > 0" class="mt-4">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-popconfirm title="确定要删除选中的机柜吗？" @confirm="handleDelete(selectedRowKeys)">
            <a-button type="primary" danger size="small">批量删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <BasicModal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑机柜' : '新增机柜'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
      width="700px"
      class="cabinet-modal"
    >
      <BasicForm @register="registerCabinetForm" />
    </BasicModal>

    <!-- 绑定设备弹窗 -->
    <BasicModal
      v-model:open="bindModalVisible"
      title="绑定设备到机柜"
      @ok="handleBindSubmit"
      @cancel="handleBindCancel"
      :confirm-loading="bindSubmitLoading"
      width="600px"
      class="bind-modal"
    >
      <BasicForm @register="registerBindForm" />
    </BasicModal>

    <!-- 机柜详情弹窗 -->
    <BasicModal
      v-model:open="cabinetDetailVisible"
      :title="`机柜详情 - ${currentCabinetDetail?.name || ''}`"
      @cancel="handleCabinetDetailCancel"
      width="800px"
      :footer="null"
    >
      <div v-if="currentCabinetDetail" class="py-2.5">
        <!-- 机柜信息 -->
        <div class="bg-gray-50 p-4 rounded-8px mb-5">
          <div class="grid grid-cols-2 gap-3 gap-x-4">
            <div class="flex items-center text-14px">
              <span class="font-medium text-gray-600 min-w-70px flex-shrink-0">机柜名称：</span>
              <span class="text-gray-800 font-semibold flex-1">{{ currentCabinetDetail.name }}</span>
            </div>
          </div>
        </div>

        <!-- 机柜可视化和设备信息 -->
        <div class="flex gap-6 items-start">
          <!-- 绑定设备信息 -->
          <div class="flex-shrink-0 w-300px min-h-200px bg-white border border-gray-200 rounded-8px overflow-hidden">
            <div class="flex justify-between items-center px-5 py-4 bg-gray-50 border-b border-gray-200">
              <h4 class="m-0 text-16px font-semibold text-gray-800">绑定设备 ({{ cabinetDevices.length }}台)</h4>
            </div>

            <div v-if="cabinetDetailLoading" class="flex items-center justify-center py-8 text-gray-400">
              <a-spin size="small" />
              <span class="ml-2">加载设备信息中...</span>
            </div>

            <div v-else-if="cabinetDevices.length === 0" class="py-6 text-center">
              <a-empty :image="false" description="暂无绑定设备" />
            </div>

            <div v-else class="px-5 py-4">
              <a-alert
                message="设备管理提示"
                :description="`当前机柜共有 ${cabinetDevices.length} 台设备。您可以在右侧的机柜分布图中查看详细信息，并直接在设备块上进行解绑操作。`"
                type="info"
                show-icon
                closable
                class="rounded-8px"
              />
            </div>
          </div>

          <!-- 机柜可视化 -->
          <div class="flex-1 min-w-350px">
            <h4 class="text-16px font-semibold text-gray-800 mb-4 text-center">机柜U位分布图</h4>

            <!-- 简化的机柜外框 -->
            <div class="bg-gray-100 border-2 border-gray-300 rounded-lg p-3 mx-auto mb-4 max-w-300px">
              <!-- 机柜标题 -->
              <div class="bg-gray-800 text-white px-3 py-2 rounded text-center mb-2">
                <div class="text-sm font-medium">{{ currentCabinetDetail.name }}</div>
              </div>

              <!-- U位列表 -->
              <div class="bg-white border border-gray-200 rounded p-2">
                <div class="flex flex-col-reverse gap-1 max-h-400px overflow-y-auto">
                  <div
                    v-for="(block, index) in generateDeviceBlocks(currentCabinetDetail)"
                    :key="index"
                    class="relative border rounded cursor-pointer transition-all duration-200 hover:shadow-md"
                    :class="{
                      'bg-red-100 border-red-300 text-red-800': block.type === 'used',
                      'bg-green-100 border-green-300 text-green-800': block.type === 'available',
                      'bg-gray-100 border-gray-300 text-gray-600': block.type === 'unknown',
                    }"
                    :style="{ minHeight: `${block.height * 32}px` }"
                  >
                    <a-tooltip
                      :title="
                        block.device
                          ? `${block.device.deviceName}${block.device.models ? ` (${block.device.models})` : ''} - U${block.startU}${block.height > 1 ? `-${block.endU}` : ''}`
                          : block.type === 'available'
                            ? '可用U位'
                            : '未知状态'
                      "
                      placement="left"
                    >
                      <div class="flex items-center justify-between h-full px-3 py-2">
                        <div class="flex-1">
                          <div class="font-mono font-semibold text-sm"> U{{ block.startU }}{{ block.height > 1 ? `-${block.endU}` : '' }} </div>
                          <div v-if="block.device" class="text-xs mt-1 truncate">
                            {{ block.device.deviceName }}
                          </div>
                          <div v-else-if="block.type === 'available'" class="text-xs mt-1 text-green-600"> 可用 </div>
                          <div v-else class="text-xs mt-1 text-gray-500"> 未知 </div>
                        </div>

                        <!-- 解绑按钮 -->
                        <div v-if="block.device" class="ml-2">
                          <a-popconfirm
                            :title="`确定要解绑设备 ${block.device.deviceName} 吗？`"
                            @confirm="handleUnbindDeviceFromBlock(block.device!)"
                            placement="left"
                          >
                            <a-button
                              type="text"
                              size="small"
                              danger
                              class="opacity-60 hover:opacity-100 flex items-center justify-center w-6 h-6 text-lg font-bold"
                            >
                              ×
                            </a-button>
                          </a-popconfirm>
                        </div>
                      </div>
                    </a-tooltip>

                    <!-- 多U位设备的分割线 -->
                    <div v-if="block.device && block.height > 1" class="absolute left-3 right-3">
                      <div
                        v-for="dividerIndex in block.height - 1"
                        :key="dividerIndex"
                        class="border-t border-dashed border-current opacity-30"
                        :style="{ top: `${dividerIndex * 32}px` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 简化的图例 -->
            <div class="flex justify-center gap-4 mt-4">
              <div class="flex items-center gap-2 text-sm">
                <div class="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                <span class="text-gray-600">已使用</span>
              </div>
              <div class="flex items-center gap-2 text-sm">
                <div class="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
                <span class="text-gray-600">可使用</span>
              </div>
              <div class="flex items-center gap-2 text-sm">
                <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                <span class="text-gray-600">未知</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import SyncOutlined from '@ant-design/icons-vue/lib/icons/SyncOutlined';
  import {
    getAssetCabinetList,
    getAssetCabinetById,
    addAssetCabinet,
    updateAssetCabinet,
    deleteAssetCabinet,
    bindDeviceToCabinet,
    getCabinetDevicesByCabinetId,
    unbindCabinetDevices,
    type AssetCabinetModel,
    type AssetCabinetParams,
    type CabinetBindDeviceParams,
    type CabinetDeviceInfo,
  } from '/@/api/asset/cabinet';
  import { getRoomList, type RoomModel } from '/@/api/asset/room';
  import { getAssetDeviceList, type AssetDeviceModel } from '/@/api/asset/device';
  import { BasicModal } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { safeSetFieldsValue } from '/@/utils/form';

  const DEFAULT_PAGE_SIZE = 10;

  interface SearchForm {
    name: string;
    roomId: number | undefined;
  }

  interface ExtendedAssetCabinetModel extends AssetCabinetModel {
    roomName?: string;
  }

  const loading = ref<boolean>(false);
  const submitLoading = ref<boolean>(false);
  const tableData = ref<ExtendedAssetCabinetModel[]>([]);
  const selectedRowKeys = ref<number[]>([]);
  const modalVisible = ref<boolean>(false);
  const isEdit = ref<boolean>(false);
  const roomOptions = ref<{ label: string; value: number }[]>([]);

  // 绑定设备相关
  const bindModalVisible = ref<boolean>(false);
  const bindSubmitLoading = ref<boolean>(false);
  const deviceOptions = ref<{ label: string; value: number }[]>([]);
  const currentCabinet = ref<ExtendedAssetCabinetModel | null>(null);

  // 机柜详情相关
  const cabinetDetailVisible = ref<boolean>(false);
  const currentCabinetDetail = ref<ExtendedAssetCabinetModel | null>(null);
  const cabinetDevices = ref<CabinetDeviceInfo[]>([]);
  const cabinetDetailLoading = ref<boolean>(false);

  const searchForm = reactive<SearchForm>({
    name: '',
    roomId: undefined,
  });

  const cabinetFormSchema: FormSchema[] = [
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
    {
      label: '机柜名称',
      field: 'name',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜名称',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入机柜名称' },
        { min: 1, max: 50, message: '机柜名称长度应在1-50个字符之间' },
      ],
    },
    {
      label: '机柜编号',
      field: 'code',
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜编号',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [{ min: 1, max: 50, message: '机柜编号长度应在1-50个字符之间' }],
    },
    {
      label: '房间',
      field: 'roomId',
      required: true,
      component: 'Select',
      componentProps: () => ({
        placeholder: '请选择房间',
        class: 'w-full',
        options: roomOptions.value,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option?.label?.toLowerCase().includes(input.toLowerCase());
        },
      }),
      colProps: { span: 24 },
      rules: [{ required: true, message: '请选择房间' }],
    },
    {
      label: '总U位',
      field: 'totalU',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max: 100,
        placeholder: '请输入总U位',
        class: 'w-full',
      },
      colProps: { span: 24 },
      rules: [
        { required: true, message: '请输入总U位' },
        { type: 'number', min: 1, max: 100, message: '总U位应在1-100之间' },
      ],
    },
    {
      label: '备注',
      field: 'remark',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
        maxlength: 500,
      },
      colProps: { span: 24 },
      rules: [{ max: 500, message: '备注长度不能超过500个字符' }],
    },
  ];

  const [registerCabinetForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: cabinetFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100,
  });

  // 绑定设备表单配置
  const bindFormSchema: FormSchema[] = [
    {
      label: '机柜名称',
      field: 'cabinetName',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      colProps: { span: 24 },
    },
    {
      label: '选择设备',
      field: 'assDeviceId',
      required: true,
      component: 'Select',
      componentProps: () => ({
        placeholder: '请选择要绑定的设备',
        class: 'w-full',
        options: deviceOptions.value,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option?.label?.toLowerCase().includes(input.toLowerCase());
        },
      }),
      colProps: { span: 24 },
      rules: [{ required: true, message: '请选择要绑定的设备' }],
    },
    {
      label: '起始U位',
      field: 'startU',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入起始U位',
        class: 'w-full',
      },
      colProps: { span: 24 },
      rules: [
        { required: true, message: '请输入起始U位' },
        { type: 'number', min: 1, message: '起始U位必须大于0' },
      ],
    },
  ];

  const [registerBindForm, { resetFields: resetBindFields, setFieldsValue: setBindFieldsValue, validate: validateBind }] = useForm({
    schemas: bindFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100,
  });

  const pagination = reactive({
    current: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  const columns = [
    {
      title: '机柜名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '机柜编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '房间',
      dataIndex: 'roomName',
      key: 'roomName',
      width: 200,
    },
    {
      title: '总U位',
      dataIndex: 'totalU',
      key: 'totalU',
      width: 100,
      align: 'center',
    },
    {
      title: '使用情况',
      key: 'usage',
      width: 200,
    },
    {
      title: '已使用U位',
      dataIndex: 'useU',
      key: 'useU',
      width: 120,
      align: 'center',
    },
    {
      title: '未使用U位',
      dataIndex: 'unuseU',
      key: 'unuseU',
      width: 160,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 260,
      fixed: 'right',
    },
  ];

  const calculateUsagePercent = (record: AssetCabinetModel): number => {
    const total = parseInt(record.totalU) || 0;
    const used = parseInt(record.useU) || 0;
    if (total === 0) return 0;
    return Math.round((used / total) * 100);
  };

  const getUsageStatus = (record: AssetCabinetModel): string => {
    const percent = calculateUsagePercent(record);
    if (percent >= 90) return 'exception';
    if (percent >= 70) return 'active';
    return 'success';
  };

  // 获取已使用U位的颜色
  const getUsedUColor = (record: AssetCabinetModel): string => {
    const percent = calculateUsagePercent(record);
    if (percent >= 90) return 'red';
    if (percent >= 70) return 'orange';
    if (percent >= 40) return 'blue';
    return 'cyan';
  };

  // 解析并格式化未使用U位字符串，返回可用U位数量
  const getUnusedUCount = (record: AssetCabinetModel): number => {
    if (!record.unuseU) return 0;

    // 清理字符串：移除所有空格，统一分隔符为逗号
    const cleanStr = record.unuseU
      .toString()
      .replace(/\s+/g, '') // 移除所有空格
      .replace(/[.，、]/g, ',') // 将各种分隔符统一为逗号
      .replace(/,+/g, ',') // 合并多个连续逗号
      .replace(/^,|,$/g, ''); // 移除首尾逗号

    if (!cleanStr) return 0;

    // 分割并过滤出有效数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0);

    return numbers.length;
  };

  // 格式化未使用U位的详细信息用于tooltip显示
  const formatUnusedUTooltip = (unuseU: string | undefined): string => {
    if (!unuseU) return '暂无可用U位';

    // 清理字符串
    const cleanStr = unuseU
      .toString()
      .replace(/\s+/g, '')
      .replace(/[.，、]/g, ',')
      .replace(/,+/g, ',')
      .replace(/^,|,$/g, '');

    if (!cleanStr) return '暂无可用U位';

    // 解析并排序数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0)
      .sort((a, b) => a - b);

    if (numbers.length === 0) return '暂无可用U位';

    // 将连续数字合并为范围显示
    const ranges: string[] = [];
    let start = numbers[0];
    let end = numbers[0];

    for (let i = 1; i < numbers.length; i++) {
      if (numbers[i] === end + 1) {
        end = numbers[i];
      } else {
        ranges.push(start === end ? `${start}` : `${start}-${end}`);
        start = end = numbers[i];
      }
    }
    ranges.push(start === end ? `${start}` : `${start}-${end}`);

    const rangeStr = ranges.join(', ');
    return `可用U位: ${rangeStr} (共${numbers.length}个)`;
  };

  // 显示机柜详情弹窗
  const showCabinetDetail = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    currentCabinetDetail.value = record;
    cabinetDetailVisible.value = true;
    cabinetDetailLoading.value = true;

    try {
      // 获取机柜绑定的设备信息
      const devices = await getCabinetDevicesByCabinetId(record.id);
      cabinetDevices.value = devices;
    } catch (error) {
      console.error('获取机柜设备信息失败:', error);
      message.error('获取设备信息失败');
      cabinetDevices.value = [];
    } finally {
      cabinetDetailLoading.value = false;
    }
  };

  // 关闭机柜详情弹窗
  const handleCabinetDetailCancel = (): void => {
    cabinetDetailVisible.value = false;
    currentCabinetDetail.value = null;
    cabinetDevices.value = [];
  };

  // 获取可用的U位位置数组
  const getAvailableUPositions = (record: AssetCabinetModel): number[] => {
    if (!record.unuseU) return [];

    // 清理并解析未使用U位字符串
    const cleanStr = record.unuseU
      .toString()
      .replace(/\s+/g, '')
      .replace(/[.，、]/g, ',')
      .replace(/,+/g, ',')
      .replace(/^,|,$/g, '');

    if (!cleanStr) return [];

    // 解析数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0);

    return numbers;
  };

  // 设备块类型定义
  interface DeviceBlock {
    startU: number;
    endU: number;
    device: CabinetDeviceInfo | null;
    type: 'used' | 'available' | 'unknown';
    height: number;
  }

  // 生成合并后的设备块列表
  const generateDeviceBlocks = (record: AssetCabinetModel): DeviceBlock[] => {
    const totalU = parseInt(record.totalU);
    const availableUPositions = getAvailableUPositions(record);
    const blocks: DeviceBlock[] = [];

    // 创建一个U位状态映射
    const uPositionMap: { [key: number]: { device: CabinetDeviceInfo | null; type: 'used' | 'available' | 'unknown' } } = {};

    // 初始化所有U位为未知状态
    for (let i = 1; i <= totalU; i++) {
      uPositionMap[i] = { device: null, type: 'unknown' };
    }

    // 标记可用U位
    availableUPositions.forEach((u) => {
      if (uPositionMap[u]) {
        uPositionMap[u].type = 'available';
      }
    });

    // 标记设备占用的U位
    cabinetDevices.value.forEach((device: CabinetDeviceInfo) => {
      const startU = device.startU;
      const endU = device.endU || startU + (device.uSize || 1) - 1;
      for (let i = startU; i <= endU; i++) {
        if (uPositionMap[i]) {
          uPositionMap[i] = { device, type: 'used' };
        }
      }
    });

    // 只合并已绑定设备的连续U位，其他U位保持单独显示
    let currentBlock: DeviceBlock | null = null;

    for (let i = totalU; i >= 1; i--) {
      // 从上到下（高U位到低U位）
      const current = uPositionMap[i];

      if (!currentBlock) {
        // 开始新块
        currentBlock = {
          startU: i,
          endU: i,
          device: current.device,
          type: current.type,
          height: 1,
        };
      } else {
        // 只合并相同设备的连续U位，其他类型不合并
        const canMerge = current.type === 'used' && currentBlock.type === 'used' && current.device?.deviceId === currentBlock.device?.deviceId;

        if (canMerge) {
          // 合并到当前块
          currentBlock.startU = i;
          currentBlock.height++;
        } else {
          // 完成当前块，开始新块
          blocks.push(currentBlock);
          currentBlock = {
            startU: i,
            endU: i,
            device: current.device,
            type: current.type,
            height: 1,
          };
        }
      }
    }

    // 添加最后一个块
    if (currentBlock) {
      blocks.push(currentBlock);
    }

    return blocks;
  };

  // 在设备块上解绑设备
  const handleUnbindDeviceFromBlock = async (device: CabinetDeviceInfo): Promise<void> => {
    if (!currentCabinetDetail.value) return;

    // 检查设备ID字段
    if (!device.deviceId) {
      console.error('设备ID为空或未定义:', device);
      message.error('设备ID无效，无法解绑');
      return;
    }

    try {
      await unbindCabinetDevices({
        cabinetId: currentCabinetDetail.value.id,
        deviceId: device.deviceId.toString(),
      });
      message.success('设备解绑成功');

      // 重新获取设备信息
      const devices = await getCabinetDevicesByCabinetId(currentCabinetDetail.value.id);
      cabinetDevices.value = devices;

      // 重新获取机柜详细信息，更新unuseU字段
      try {
        const updatedCabinet = await getAssetCabinetById(currentCabinetDetail.value.id);
        const room = roomOptions.value.find((room: { label: string; value: number }) => room.value === updatedCabinet.roomId);
        currentCabinetDetail.value = {
          ...updatedCabinet,
          roomName: room ? room.label : `房间ID: ${updatedCabinet.roomId}`,
        };
      } catch (cabinetError) {
        console.error('获取机柜详细信息失败:', cabinetError);
        // 即使获取机柜信息失败，也不影响设备解绑的成功状态
      }

      // 刷新列表数据
      loadData();
    } catch (error) {
      console.error('解绑设备失败:', error);
      message.error('解绑设备失败');
    }
  };

  const filterOption = (input: string, option: any): boolean => {
    return option?.label?.toLowerCase().includes(input.toLowerCase());
  };

  const buildParams = (): AssetCabinetParams => {
    return {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      roomId: searchForm.roomId || undefined,
    };
  };

  const resetSearchForm = (): void => {
    searchForm.name = '';
    searchForm.roomId = undefined;
  };

  const loadRoomOptions = async (): Promise<void> => {
    try {
      const result = await getRoomList({ current: 1, size: 1000 });
      roomOptions.value = result.records.map((room: RoomModel) => ({
        label: `${room.roomName} (${room.floors}楼)`,
        value: room.id,
      }));
    } catch (error) {
      console.error('加载房间数据失败:', error);
      message.error('加载房间数据失败');
    }
  };

  // 加载设备选项数据
  const loadDeviceOptions = async (): Promise<void> => {
    try {
      const result = await getAssetDeviceList({ pageNo: 1, pageSize: 1000 });
      deviceOptions.value = result.records.map((device: AssetDeviceModel) => ({
        label: `${device.deviceName} (${device.models})`,
        value: device.id,
      }));
    } catch (error) {
      console.error('加载设备数据失败:', error);
      message.error('加载设备数据失败');
    }
  };

  const loadData = async (): Promise<void> => {
    try {
      loading.value = true;
      const params = buildParams();
      const result = await getAssetCabinetList(params);

      tableData.value = result.records.map((cabinet: AssetCabinetModel): ExtendedAssetCabinetModel => {
        const room = roomOptions.value.find((room: { label: string; value: number }) => room.value === cabinet.roomId);
        return {
          ...cabinet,
          roomName: room ? room.label : `房间ID: ${cabinet.roomId}`,
        };
      });
      pagination.total = result.total;
    } catch (error) {
      console.error('加载机柜数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = (): void => {
    pagination.current = 1;
    loadData();
  };

  const handleReset = (): void => {
    resetSearchForm();
    pagination.current = 1;
    loadData();
  };

  const handleRefresh = (): void => {
    loadData();
    message.success('数据已刷新');
  };

  const handleTableChange = (pag: any): void => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadData();
  };

  const onSelectChange = (keys: number[]): void => {
    selectedRowKeys.value = keys;
  };

  const handleAdd = async (): Promise<void> => {
    isEdit.value = false;
    modalVisible.value = true;
    resetFields();
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
  };

  const handleEdit = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    isEdit.value = true;
    modalVisible.value = true;
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
    try {
      await safeSetFieldsValue(setFieldsValue, {
        ...record,
        totalU: parseInt(record.totalU),
      });
    } catch (error) {
      console.error('设置表单值失败:', error);
      message.error('加载编辑数据失败');
    }
  };

  const handleSubmit = async (): Promise<void> => {
    try {
      const values = await validate();
      submitLoading.value = true;

      const submitData = {
        ...values,
        totalU: values.totalU.toString(),
        // 新增时不发送useU和unuseU，让后端处理
        // 编辑时保留原有的useU和unuseU值
      };

      if (isEdit.value) {
        const updateData: AssetCabinetModel = {
          ...submitData,
          id: typeof values.id === 'string' ? parseInt(values.id, 10) : values.id,
        };

        if (!updateData.id || isNaN(updateData.id)) {
          throw new Error('无效的机柜ID');
        }

        await updateAssetCabinet(updateData);
      } else {
        const { id, ...addData } = submitData;
        await addAssetCabinet(addData);
      }

      message.success(isEdit.value ? '修改成功' : '新增成功');
      modalVisible.value = false;
      loadData();
    } catch (error) {
      console.error('提交失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '操作失败';
      // message.error(errorMessage);
    } finally {
      submitLoading.value = false;
    }
  };

  const handleCancel = (): void => {
    modalVisible.value = false;
    resetFields();
  };

  const handleDelete = async (ids: number[]): Promise<void> => {
    if (!ids || ids.length === 0) {
      message.warning('请选择要删除的机柜');
      return;
    }

    try {
      await deleteAssetCabinet(ids);
      message.success('删除成功');
      selectedRowKeys.value = [];
      loadData();
    } catch (error) {
      console.error('删除失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '删除失败';
      // message.error(errorMessage);
    }
  };

  // 绑定设备相关方法
  const handleBindDevice = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    currentCabinet.value = record;

    // 确保设备选项已加载
    if (deviceOptions.value.length === 0) {
      await loadDeviceOptions();
    }

    // 打开弹窗，表单初始化将在watch中处理
    bindModalVisible.value = true;
  };

  const handleBindSubmit = async (): Promise<void> => {
    try {
      const values = await validateBind();
      bindSubmitLoading.value = true;

      const bindData: CabinetBindDeviceParams = {
        assDeviceId: values.assDeviceId,
        cabinetId: currentCabinet.value!.id,
        startU: values.startU,
      };

      await bindDeviceToCabinet(bindData);
      message.success('设备绑定成功');
      bindModalVisible.value = false;
      loadData(); // 刷新列表以更新使用情况
    } catch (error) {
      console.error('绑定失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '绑定失败';
      // message.error(errorMessage);
    } finally {
      bindSubmitLoading.value = false;
    }
  };

  const handleBindCancel = (): void => {
    bindModalVisible.value = false;
    resetBindFields();
    currentCabinet.value = null;
  };

  // 监听绑定弹窗状态，在弹窗打开后初始化表单
  watch(bindModalVisible, async (newVal) => {
    if (newVal && currentCabinet.value) {
      // 等待弹窗完全渲染
      await nextTick();

      // 延迟一点时间确保表单完全初始化
      setTimeout(async () => {
        try {
          resetBindFields();
          await setBindFieldsValue({
            cabinetName: currentCabinet.value!.name,
          });
        } catch (error) {
          console.warn('表单初始化失败:', error);
          // 再次尝试
          setTimeout(async () => {
            try {
              resetBindFields();
              await setBindFieldsValue({
                cabinetName: currentCabinet.value!.name,
              });
            } catch (retryError) {
              console.error('表单初始化重试失败:', retryError);
            }
          }, 200);
        }
      }, 50);
    }
  });

  onMounted(async () => {
    await loadRoomOptions();
    loadData();
  });
</script>
<style scoped lang="less">
  // 表格样式优化
  :deep(.ant-table-tbody) {
    .ant-table-cell {
      padding: 12px 8px;
    }
  }

  :deep(.ant-progress-text) {
    font-size: 12px;
    font-weight: 500;
  }

  // 简化的滚动条样式
  .cabinet-u-slots::-webkit-scrollbar {
    width: 6px;
  }

  .cabinet-u-slots::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .cabinet-u-slots::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .cabinet-u-slots::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  // 搜索表单样式
  .cabinet-search-form {
    :deep(.ant-form-item) {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      margin-right: 16px;

      .ant-form-item-label {
        flex: none;
        width: auto;
        margin-right: 8px;

        label {
          white-space: nowrap;
        }
      }

      .ant-form-item-control {
        flex: 1;
        min-width: 0;
      }
    }
  }
</style>
